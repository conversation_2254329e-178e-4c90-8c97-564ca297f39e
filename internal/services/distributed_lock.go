package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/bsm/redislock"
	"github.com/rs/zerolog/log"
)

// DistributedLockManager handles distributed locking operations
type DistributedLockManager struct {
	locker            *redislock.Client
	defaultTimeout    time.Duration
	defaultRetryDelay time.Duration
	defaultMaxRetries int
	refreshInterval   time.Duration
	cleanupTimeout    time.Duration
	keyPrefix         string // Prefix for all lock keys
	// Lock tracking for cleanup
	activeLocks map[string]*DistributedLock
	locksMutex  sync.RWMutex
}

// NewDistributedLockManager creates a new distributed lock manager
func NewDistributedLockManager(redisService *RedisService) *DistributedLockManager {
	config := redisService.GetConfig()
	return &DistributedLockManager{
		locker:            redisService.GetLocker(),
		defaultTimeout:    config.LockTimeout,
		defaultRetryDelay: config.LockRetryDelay,
		defaultMaxRetries: config.LockMaxRetries,
		refreshInterval:   config.LockRefreshInterval,
		cleanupTimeout:    config.LockCleanupTimeout,
		keyPrefix:         config.KeyPrefix,
		activeLocks:       make(map[string]*DistributedLock),
		locksMutex:        sync.RWMutex{},
	}
}

// LockOptions contains options for acquiring a distributed lock
type LockOptions struct {
	Timeout    time.Duration // Lock timeout (TTL)
	RetryDelay time.Duration // Delay between retry attempts
	MaxRetries int           // Maximum number of retry attempts
	Metadata   string        // Optional metadata to store with the lock
}

// DistributedLock represents an acquired distributed lock
type DistributedLock struct {
	lock    *redislock.Lock
	manager *DistributedLockManager
	key     string
	ctx     context.Context
	cancel  context.CancelFunc
}

// AcquireLock attempts to acquire a distributed lock with the given key
func (dlm *DistributedLockManager) AcquireLock(ctx context.Context, key string, opts *LockOptions) (*DistributedLock, error) {
	// Use defaults if options not provided
	if opts == nil {
		opts = &LockOptions{
			Timeout:    dlm.defaultTimeout,
			RetryDelay: dlm.defaultRetryDelay,
			MaxRetries: dlm.defaultMaxRetries,
		}
	}

	// Apply defaults for missing values
	if opts.Timeout == 0 {
		opts.Timeout = dlm.defaultTimeout
	}
	if opts.RetryDelay == 0 {
		opts.RetryDelay = dlm.defaultRetryDelay
	}
	if opts.MaxRetries == 0 {
		opts.MaxRetries = dlm.defaultMaxRetries
	}

	// Create retry strategy
	retryStrategy := redislock.LimitRetry(
		redislock.LinearBackoff(opts.RetryDelay),
		opts.MaxRetries,
	)

	// Create lock options
	lockOpts := &redislock.Options{
		RetryStrategy: retryStrategy,
		Metadata:      opts.Metadata,
	}

	log.Debug().
		Str("key", key).
		Dur("timeout", opts.Timeout).
		Dur("retry_delay", opts.RetryDelay).
		Int("max_retries", opts.MaxRetries).
		Msg("Attempting to acquire distributed lock")

	// Attempt to acquire the lock
	lock, err := dlm.locker.Obtain(ctx, key, opts.Timeout, lockOpts)
	if err != nil {
		if err == redislock.ErrNotObtained {
			log.Warn().
				Str("key", key).
				Dur("timeout", opts.Timeout).
				Int("max_retries", opts.MaxRetries).
				Msg("Failed to acquire distributed lock after retries")
			return nil, fmt.Errorf("failed to acquire lock for key '%s' after %d retries: %w", key, opts.MaxRetries, err)
		}
		log.Error().
			Err(err).
			Str("key", key).
			Msg("Error acquiring distributed lock")
		return nil, fmt.Errorf("error acquiring lock for key '%s': %w", key, err)
	}

	log.Info().
		Str("key", key).
		Str("token", lock.Token()).
		Dur("timeout", opts.Timeout).
		Msg("Successfully acquired distributed lock")

	// Create context for lock management
	lockCtx, cancel := context.WithCancel(context.Background())

	distributedLock := &DistributedLock{
		lock:    lock,
		manager: dlm,
		key:     key,
		ctx:     lockCtx,
		cancel:  cancel,
	}

	// Track the lock for cleanup
	dlm.locksMutex.Lock()
	dlm.activeLocks[key] = distributedLock
	dlm.locksMutex.Unlock()

	// Start lock refresh goroutine if refresh interval is configured
	if dlm.refreshInterval > 0 {
		go distributedLock.refreshLoop()
	}

	return distributedLock, nil
}

// ReleaseLock releases the distributed lock
func (dl *DistributedLock) ReleaseLock() error {
	if dl == nil || dl.lock == nil {
		return fmt.Errorf("lock is nil or already released")
	}

	// Cancel the refresh goroutine
	if dl.cancel != nil {
		dl.cancel()
	}

	// Release the lock
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := dl.lock.Release(ctx)
	if err != nil {
		if err == redislock.ErrLockNotHeld {
			log.Warn().
				Str("key", dl.key).
				Msg("Lock was already released or expired")
			return nil // Not an error if lock was already released
		}
		log.Error().
			Err(err).
			Str("key", dl.key).
			Msg("Error releasing distributed lock")
		return fmt.Errorf("error releasing lock for key '%s': %w", dl.key, err)
	}

	log.Info().
		Str("key", dl.key).
		Msg("Successfully released distributed lock")

	// Remove from active locks tracking
	if dl.manager != nil {
		dl.manager.locksMutex.Lock()
		delete(dl.manager.activeLocks, dl.key)
		dl.manager.locksMutex.Unlock()
	}

	return nil
}

// GetKey returns the lock key
func (dl *DistributedLock) GetKey() string {
	if dl == nil {
		return ""
	}
	return dl.key
}

// GetTTL returns the remaining time-to-live of the lock
func (dl *DistributedLock) GetTTL() (time.Duration, error) {
	if dl == nil || dl.lock == nil {
		return 0, fmt.Errorf("lock is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	return dl.lock.TTL(ctx)
}

// refreshLoop periodically refreshes the lock to prevent expiration
func (dl *DistributedLock) refreshLoop() {
	ticker := time.NewTicker(dl.manager.refreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-dl.ctx.Done():
			log.Debug().
				Str("key", dl.key).
				Msg("Lock refresh loop stopped")
			return
		case <-ticker.C:
			if err := dl.refreshLock(); err != nil {
				log.Error().
					Err(err).
					Str("key", dl.key).
					Msg("Failed to refresh lock, stopping refresh loop")
				return
			}
		}
	}
}

// refreshLock extends the lock TTL
func (dl *DistributedLock) refreshLock() error {
	if dl == nil || dl.lock == nil {
		return fmt.Errorf("lock is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := dl.lock.Refresh(ctx, dl.manager.defaultTimeout, nil)
	if err != nil {
		if err == redislock.ErrNotObtained {
			log.Warn().
				Str("key", dl.key).
				Msg("Lock expired during refresh")
			return err
		}
		log.Error().
			Err(err).
			Str("key", dl.key).
			Msg("Error refreshing lock")
		return err
	}

	log.Debug().
		Str("key", dl.key).
		Dur("new_ttl", dl.manager.defaultTimeout).
		Msg("Successfully refreshed lock")

	return nil
}

// CreateTweetLockKey creates a standardized lock key for tweet processing
func CreateTweetLockKey(tweetID string) string {
	return fmt.Sprintf("tweet_lock:%s", tweetID)
}

// CreateTweetLockKeyWithPrefix creates a standardized lock key with prefix for tweet processing
func CreateTweetLockKeyWithPrefix(prefix, tweetID string) string {
	return fmt.Sprintf("%stweet_lock:%s", prefix, tweetID)
}

// CreateTweetLockKeyForManager creates a lock key using the manager's configured prefix
func (dlm *DistributedLockManager) CreateTweetLockKey(tweetID string) string {
	return CreateTweetLockKeyWithPrefix(dlm.keyPrefix, tweetID)
}

// CleanupAllLocks releases all active locks held by this manager instance
// This method is designed to be called during graceful shutdown
func (dlm *DistributedLockManager) CleanupAllLocks(ctx context.Context) error {
	if dlm == nil {
		return fmt.Errorf("lock manager is nil")
	}

	// Create a context with timeout for cleanup operations
	cleanupCtx, cancel := context.WithTimeout(ctx, dlm.cleanupTimeout)
	defer cancel()

	// Get a snapshot of all active locks
	dlm.locksMutex.RLock()
	lockKeys := make([]string, 0, len(dlm.activeLocks))
	locks := make([]*DistributedLock, 0, len(dlm.activeLocks))
	for key, lock := range dlm.activeLocks {
		lockKeys = append(lockKeys, key)
		locks = append(locks, lock)
	}
	dlm.locksMutex.RUnlock()

	if len(locks) == 0 {
		log.Info().Msg("No active locks to cleanup")
		return nil
	}

	log.Info().
		Int("lock_count", len(locks)).
		Strs("lock_keys", lockKeys).
		Dur("cleanup_timeout", dlm.cleanupTimeout).
		Msg("Starting cleanup of all active locks")

	// Track cleanup results
	var cleanupErrors []error
	successCount := 0
	errorCount := 0

	// Release all locks concurrently with a semaphore to limit concurrency
	semaphore := make(chan struct{}, 10) // Limit to 10 concurrent releases
	var wg sync.WaitGroup
	var errorsMutex sync.Mutex

	for _, lock := range locks {
		wg.Add(1)
		go func(dl *DistributedLock) {
			defer wg.Done()

			// Acquire semaphore
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }()
			case <-cleanupCtx.Done():
				errorsMutex.Lock()
				cleanupErrors = append(cleanupErrors, fmt.Errorf("cleanup timeout reached for lock %s", dl.key))
				errorCount++
				errorsMutex.Unlock()
				return
			}

			// Release the lock
			if err := dl.ReleaseLock(); err != nil {
				log.Error().
					Err(err).
					Str("key", dl.key).
					Msg("Failed to release lock during cleanup")
				errorsMutex.Lock()
				cleanupErrors = append(cleanupErrors, fmt.Errorf("failed to release lock %s: %w", dl.key, err))
				errorCount++
				errorsMutex.Unlock()
			} else {
				log.Debug().
					Str("key", dl.key).
					Msg("Successfully released lock during cleanup")
				errorsMutex.Lock()
				successCount++
				errorsMutex.Unlock()
			}
		}(lock)
	}

	// Wait for all cleanup operations to complete or timeout
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// All cleanup operations completed
	case <-cleanupCtx.Done():
		log.Warn().
			Dur("timeout", dlm.cleanupTimeout).
			Msg("Cleanup timeout reached, some locks may not have been released")
	}

	// Log cleanup results
	log.Info().
		Int("total_locks", len(locks)).
		Int("successful_releases", successCount).
		Int("failed_releases", errorCount).
		Msg("Lock cleanup completed")

	// Return error if any cleanup operations failed
	if len(cleanupErrors) > 0 {
		return fmt.Errorf("cleanup completed with %d errors: %v", len(cleanupErrors), cleanupErrors)
	}

	return nil
}

// GetActiveLockCount returns the number of currently active locks
func (dlm *DistributedLockManager) GetActiveLockCount() int {
	if dlm == nil {
		return 0
	}

	dlm.locksMutex.RLock()
	defer dlm.locksMutex.RUnlock()
	return len(dlm.activeLocks)
}

// GetActiveLockKeys returns a slice of all currently active lock keys
func (dlm *DistributedLockManager) GetActiveLockKeys() []string {
	if dlm == nil {
		return nil
	}

	dlm.locksMutex.RLock()
	defer dlm.locksMutex.RUnlock()

	keys := make([]string, 0, len(dlm.activeLocks))
	for key := range dlm.activeLocks {
		keys = append(keys, key)
	}
	return keys
}
