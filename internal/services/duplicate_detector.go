package services

import (
	"math"
	"regexp"
	"strings"
	"time"

	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/db"

	"github.com/rs/zerolog/log"
)

// DuplicateDetector handles duplicate content detection
type DuplicateDetector struct {
	config config.DuplicateDetectorConfig
	db     *db.Database
}

// NewDuplicateDetector creates a new duplicate detector
func NewDuplicateDetector(cfg config.DuplicateDetectorConfig, database *db.Database) *DuplicateDetector {
	return &DuplicateDetector{
		config: cfg,
		db:     database,
	}
}

// DetectAndMarkDuplicates detects duplicate content and marks older tweets as outdated
func (d *DuplicateDetector) DetectAndMarkDuplicates(newTweetText, newTweetID, userID string) error {
	if !d.config.Enabled {
		return nil
	}

	start := time.Now()
	log.Debug().
		Str("tweet_id", newTweetID).
		Str("user_id", userID).
		Msg("Starting duplicate content detection")

	// Get user's recent tweets for comparison
	recentTweets, err := d.getUserRecentTweets(userID, newTweetID)
	if err != nil {
		log.Error().Err(err).
			Str("user_id", userID).
			Msg("Failed to get user recent tweets for duplicate detection")
		return err
	}

	if len(recentTweets) == 0 {
		log.Debug().
			Str("user_id", userID).
			Msg("No recent tweets found for comparison")
		return nil
	}

	// Preprocess the new tweet text
	newTweetProcessed := d.preprocessText(newTweetText)

	var tweetsToMarkOutdated []string

	// Compare with each recent tweet
	for _, tweet := range recentTweets {
		if tweet.IsOutdated {
			continue // Skip already outdated tweets
		}

		tweetProcessed := d.preprocessText(tweet.TextContent)
		similarity := d.calculateJaccardSimilarity(newTweetProcessed, tweetProcessed)

		log.Debug().
			Str("new_tweet_id", newTweetID).
			Str("existing_tweet_id", tweet.TweetID).
			Float64("similarity", similarity).
			Msg("Calculated similarity")

		if similarity >= d.config.SimilarityThreshold {
			tweetsToMarkOutdated = append(tweetsToMarkOutdated, tweet.TweetID)
			log.Info().
				Str("new_tweet_id", newTweetID).
				Str("outdated_tweet_id", tweet.TweetID).
				Float64("similarity", similarity).
				Msg("Found duplicate content, marking older tweet as outdated")
		}
	}

	// Mark tweets as outdated in a transaction
	if len(tweetsToMarkOutdated) > 0 {
		if err := d.markTweetsAsOutdated(tweetsToMarkOutdated, newTweetID); err != nil {
			log.Error().Err(err).
				Strs("outdated_tweet_ids", tweetsToMarkOutdated).
				Str("new_tweet_id", newTweetID).
				Msg("Failed to mark tweets as outdated")
			return err
		}
	}

	log.Debug().
		Str("tweet_id", newTweetID).
		Int("tweets_compared", len(recentTweets)).
		Int("tweets_marked_outdated", len(tweetsToMarkOutdated)).
		Dur("duration", time.Since(start)).
		Msg("Completed duplicate content detection")

	return nil
}

// getUserRecentTweets gets user's recent tweets for comparison
func (d *DuplicateDetector) getUserRecentTweets(userID, excludeTweetID string) ([]db.Tweet, error) {
	// Calculate the time window
	timeWindow := time.Now().AddDate(0, 0, -d.config.TimeWindowDays)

	return d.db.GetUserRecentTweets(userID, excludeTweetID, timeWindow, d.config.MaxTweetsToCompare)
}

// markTweetsAsOutdated marks tweets as outdated in a database transaction
func (d *DuplicateDetector) markTweetsAsOutdated(tweetIDs []string, newTweetID string) error {
	return d.db.MarkTweetsAsOutdated(tweetIDs, newTweetID)
}

// preprocessText preprocesses text for similarity comparison
func (d *DuplicateDetector) preprocessText(text string) string {
	// Convert to lowercase
	text = strings.ToLower(text)

	// Remove URLs
	urlRegex := regexp.MustCompile(`https?://[^\s]+`)
	text = urlRegex.ReplaceAllString(text, "")

	// Remove mentions (@username)
	mentionRegex := regexp.MustCompile(`@\w+`)
	text = mentionRegex.ReplaceAllString(text, "")

	// Remove hashtags (optional - keep the text but remove #)
	hashtagRegex := regexp.MustCompile(`#(\w+)`)
	text = hashtagRegex.ReplaceAllString(text, "$1")

	// Remove extra whitespace and punctuation
	spaceRegex := regexp.MustCompile(`\s+`)
	text = spaceRegex.ReplaceAllString(text, " ")

	// Remove common punctuation
	punctRegex := regexp.MustCompile(`[^\w\s]`)
	text = punctRegex.ReplaceAllString(text, "")

	return strings.TrimSpace(text)
}

// calculateJaccardSimilarity calculates Jaccard similarity between two texts
func (d *DuplicateDetector) calculateJaccardSimilarity(text1, text2 string) float64 {
	if text1 == "" && text2 == "" {
		return 1.0
	}
	if text1 == "" || text2 == "" {
		return 0.0
	}

	// Split into word sets
	words1 := d.getWordSet(text1)
	words2 := d.getWordSet(text2)

	// Calculate intersection and union
	intersection := make(map[string]bool)
	for word := range words1 {
		if words2[word] {
			intersection[word] = true
		}
	}

	union := make(map[string]bool)
	for word := range words1 {
		union[word] = true
	}
	for word := range words2 {
		union[word] = true
	}

	if len(union) == 0 {
		return 0.0
	}

	return float64(len(intersection)) / float64(len(union))
}

// getWordSet converts text to a set of words
func (d *DuplicateDetector) getWordSet(text string) map[string]bool {
	words := strings.Fields(text)
	wordSet := make(map[string]bool)

	for _, word := range words {
		if len(word) > 2 { // Ignore very short words
			wordSet[word] = true
		}
	}

	return wordSet
}

// calculateCosineSimilarity calculates cosine similarity (alternative implementation)
func (d *DuplicateDetector) calculateCosineSimilarity(text1, text2 string) float64 {
	if text1 == "" && text2 == "" {
		return 1.0
	}
	if text1 == "" || text2 == "" {
		return 0.0
	}

	// Get word frequency vectors
	freq1 := d.getWordFrequency(text1)
	freq2 := d.getWordFrequency(text2)

	// Calculate dot product and magnitudes
	var dotProduct, magnitude1, magnitude2 float64

	allWords := make(map[string]bool)
	for word := range freq1 {
		allWords[word] = true
	}
	for word := range freq2 {
		allWords[word] = true
	}

	for word := range allWords {
		f1 := float64(freq1[word])
		f2 := float64(freq2[word])

		dotProduct += f1 * f2
		magnitude1 += f1 * f1
		magnitude2 += f2 * f2
	}

	if magnitude1 == 0 || magnitude2 == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(magnitude1) * math.Sqrt(magnitude2))
}

// getWordFrequency returns word frequency map
func (d *DuplicateDetector) getWordFrequency(text string) map[string]int {
	words := strings.Fields(text)
	freq := make(map[string]int)

	for _, word := range words {
		if len(word) > 2 {
			freq[word]++
		}
	}

	return freq
}
