package db

import (
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"

	"real-time-ca-service/internal/config"
)

// Connect establishes a connection to the database using GORM
func Connect(config config.DatabaseConfig) (*Database, error) {
	log.Debug().
		Str("host", config.Host).
		Int("port", config.Port).
		Str("user", config.User).
		Str("dbname", config.DBName).
		Str("sslmode", config.SSLMode).
		Msg("Connecting to database with GORM")

	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode,
	)

	// Configure GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Use zerolog instead
	}

	// Open connection
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	db = db.Debug()

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Info().Msg("Successfully connected to database with GORM")
	return &Database{DB: db}, nil
}

// GetTwitterUserByScreenName retrieves a Twitter user by screen name
func (d *Database) GetTwitterUserByScreenName(screenName string) (*TwitterUser, error) {
	var user TwitterUser
	err := d.Where("screen_name = ?", screenName).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// SaveTwitterUser saves a Twitter user to the database
func (d *Database) SaveTwitterUser(user *TwitterUser) error {
	now := time.Now()
	if user.FetchedAt.IsZero() {
		user.FetchedAt = now
	}
	// BaseModel 中的 UpdatedAt 会自动由 GORM 处理

	// Use GORM's Upsert functionality
	result := d.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"screen_name", "name", "followers_count", "is_verified", "profile_image_url"}),
	}).Save(user)

	return result.Error
}

// SaveTweet saves a tweet to the database
func (d *Database) SaveTweet(tweet *Tweet) error {
	now := time.Now()
	if tweet.IngestedAt.IsZero() {
		tweet.IngestedAt = now
	}

	// Use GORM's Upsert functionality
	result := d.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "tweet_id"}, {Name: "published_at"}},
		UpdateAll: true,
	}).Save(tweet)

	return result.Error
}

// AssociateTweetWithCA associates a tweet with a recognized contract address
func (d *Database) AssociateTweetWithCA(caAddress string, chainID string, tweet *Tweet, tagNames []string) error {
	now := time.Now()

	// Start a transaction
	return d.Transaction(func(tx *gorm.DB) error {
		// Check if the RecognizedCA already exists
		var recognizedCA RecognizedCA
		result := tx.Where("ca_address = ?", caAddress).First(&recognizedCA)

		if result.Error != nil {
			if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return result.Error
			}

			// Create tags for the new CA
			tags, err := d.GetOrCreateTags(tagNames)
			if err != nil {
				return err
			}

			// RecognizedCA doesn't exist, create it
			recognizedCA = RecognizedCA{
				CAAddress:      caAddress,
				ChainType:      chainID,
				AddedAt:        now,
				LastTweetAt:    tweet.PublishedAt,
				ReferenceCount: 0, // Will be incremented later by IncrementCAReference
			}

			// Create the CA first
			if err := tx.Create(&recognizedCA).Error; err != nil {
				return err
			}

			// Now associate with tags
			if len(tags) > 0 {
				if err := d.AssociateCAWithTags(recognizedCA.ID, tags); err != nil {
					return err
				}
			}
		}

		// Now associate the RecognizedCA with the tweet using GORM's Append method
		if err := tx.Model(&Tweet{BaseModel: BaseModel{ID: tweet.ID}}).Association("ExtractedCAs").Append([]*RecognizedCA{{BaseModel: BaseModel{ID: recognizedCA.ID}}}); err != nil {
			return err
		}

		return nil
	})
}

// SaveRecognizedCA saves a recognized contract address to the database
func (d *Database) SaveRecognizedCA(ca *RecognizedCA) error {
	now := time.Now()
	if ca.AddedAt.IsZero() {
		ca.AddedAt = now
	}

	// Use GORM's Upsert functionality
	result := d.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "ca_address"}},
		DoUpdates: clause.AssignmentColumns([]string{"chain_type", "token_name_hint"}),
	}).Save(ca)

	return result.Error
}

// GetOrCreateTag gets a tag by name or creates it if it doesn't exist
func (d *Database) GetOrCreateTag(tagName string) (*Tag, error) {
	var tag Tag

	// Try to find the tag first
	err := d.Where("name = ?", tagName).First(&tag).Error

	// If not found, create it
	if errors.Is(err, gorm.ErrRecordNotFound) {
		tag = Tag{
			Name:        tagName,
			Description: "",
			CreatedAt:   time.Now(),
		}

		if err := d.Create(&tag).Error; err != nil {
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}

	return &tag, nil
}

// GetOrCreateTags gets or creates multiple tags by names
func (d *Database) GetOrCreateTags(tagNames []string) ([]Tag, error) {
	var tags []Tag

	for _, name := range tagNames {
		tag, err := d.GetOrCreateTag(name)
		if err != nil {
			return nil, err
		}
		tags = append(tags, *tag)
	}

	return tags, nil
}

// AssociateTweetWithTags associates a tweet with tags
func (d *Database) AssociateTweetWithTags(tweetID int64, tags []Tag) error {
	for _, tag := range tags {
		tweetTag := TweetTag{
			TweetID: tweetID,
			TagID:   tag.ID,
		}

		if err := d.Clauses(clause.OnConflict{DoNothing: true}).Create(&tweetTag).Error; err != nil {
			return err
		}
	}

	return nil
}

// AssociateCAWithTags associates a recognized CA with tags
func (d *Database) AssociateCAWithTags(caID int64, tags []Tag) error {
	for _, tag := range tags {
		caTag := RecognizedCATag{
			RecognizedCAID: caID,
			TagID:          tag.ID,
		}

		if err := d.Clauses(clause.OnConflict{DoNothing: true}).Create(&caTag).Error; err != nil {
			return err
		}
	}

	return nil
}

// SaveTokenDetails saves token details to the database
func (d *Database) SaveTokenDetails(token *TokenDetails) error {
	now := time.Now()
	token.LastUpdatedAt = now

	// Start a transaction
	err := d.Transaction(func(tx *gorm.DB) error {
		// Save token details with upsert
		if err := tx.Clauses(clause.OnConflict{
			// Use composite key of ca_address_fk and chain_id for upsert
			Columns:   []clause.Column{{Name: "ca_address_fk"}, {Name: "chain_id"}},
			UpdateAll: true,
		}).Save(token).Error; err != nil {
			return err
		}

		// Update the last checked time for the recognized CA
		if err := tx.Model(&RecognizedCA{}).
			Where("ca_address = ?", token.CAAddressFK).
			Update("last_checked_for_data_at", now).Error; err != nil {
			return err
		}

		return nil
	})

	return err
}

// GetTweets retrieves tweets with their associated data
// Only returns tweets that have at least one TokenDetails
func (d *Database) GetTweets(limit, offset int) ([]*Tweet, error) {
	var tweets []*Tweet

	// Query tweets with their users and associated recognized CAs using GORM's preloading
	// Join with recognized_cas and token_details to ensure at least one token_details exists
	err := d.Preload("User").
		Preload("ExtractedCAs").
		Preload("ExtractedCAs.TokenDetails").
		Preload("Tags").
		Joins("INNER JOIN tweet_contract_addresses ON tweets.id = tweet_contract_addresses.tweet_id").
		Joins("INNER JOIN recognized_cas ON tweet_contract_addresses.recognized_ca_id = recognized_cas.id").
		Joins("INNER JOIN token_details ON recognized_cas.ca_address = token_details.ca_address_fk").
		Where("contains_target_keyword = ?", true).
		Group("tweets.id"). // Group to avoid duplicates
		Order("published_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tweets).Error

	if err != nil {
		return nil, err
	}

	// Populate collection tags
	if err := d.PopulateCollectionTags(tweets); err != nil {
		log.Error().Err(err).Msg("Failed to populate collection tags")
		// Don't return error, just log it as this is not critical
	}

	return tweets, nil
}

// GetListTweetsRequest represents the parameters for retrieving tweets
type GetListTweetsRequest struct {
	Limit          int      `json:"limit"`
	Offset         int      `json:"offset"`
	ContentType    string   `json:"content_type"`
	SourceType     string   `json:"source_type"`
	NoticeType     string   `json:"notice_type"`
	UserID         string   `json:"user_id"`
	UserName       string   `json:"user_name"`
	Tags           []string `json:"tags"`
	CollectionTags []string `json:"collection_tags"`
}

// GetListTweets retrieves tweets related to AI Agent with filtering options
func (d *Database) GetListTweets(req GetListTweetsRequest) ([]*Tweet, error) {
	// If userName is provided, query userId from twitter user table and override userId
	if req.UserName != "" {
		user, err := d.GetTwitterUserByScreenName(req.UserName)
		if err == nil {
			// Override userId with the one found from twitter user table
			req.UserID = user.UserID
		} else {
			return nil, nil
		}
	}

	var tweets []*Tweet

	query := d.Preload("User").
		Preload("Tags").
		Where("ai_judgment = ?", "YES").
		Where("tweet_type <> ?", "reply")

	if req.ContentType != "" && req.ContentType != "ALL" {
		query = query.Where("content_type = ?", req.ContentType)
	}

	if req.SourceType != "" && req.SourceType != "ALL" {
		query = query.Where("source_list_type = ?", req.SourceType)
	}

	if req.UserID != "" {
		query = query.Where("user_id_fk = ?", req.UserID)
	}

	switch req.NoticeType {
	case "is_others":
		query = query.Where("is_others =?", true)
	case "is_product_update":
		query = query.Where("is_product_update =?", true)
	case "is_business_data":
		query = query.Where("is_business_data =?", true)
	case "is_ecosystem_partnership":
		query = query.Where("is_ecosystem_partnership =?", true)
	case "is_profit_opportunity":
		query = query.Where("is_profit_opportunity =?", true)
	case "is_industry_event":
		query = query.Where("is_industry_event =?", true)
	default:
		// Do nothing, no specific notice type filter
	}

	if len(req.Tags) > 0 {
		// Join tweet_tags and tags tables
		query = query.Joins("INNER JOIN tweet_tags ON tweets.id = tweet_tags.tweet_id").
			Joins("INNER JOIN tags ON tweet_tags.tag_id = tags.id").
			Where("tags.name IN ?", req.Tags)
	}

	// Handle collection_tags filter
	if len(req.CollectionTags) > 0 {
		// Join collection_tags and twitter_users tables to filter by collection tags
		query = query.Joins("INNER JOIN collection_tags ON tweets.user_id_fk = (SELECT user_id FROM twitter_users WHERE screen_name = collection_tags.twitter_username)").
			Where("collection_tags.tag_name IN ?", req.CollectionTags)
	}

	err := query.Order("published_at DESC").
		Limit(req.Limit).
		Offset(req.Offset).
		Find(&tweets).Error

	if err != nil {
		return nil, err
	}

	// Populate collection tags
	if err := d.PopulateCollectionTags(tweets); err != nil {
		log.Error().Err(err).Msg("Failed to populate collection tags")
		// Don't return error, just log it as this is not critical
	}

	return tweets, nil
}

// GetTweetsByTags retrieves tweets with their associated data, filtered by tags
// If tags is empty, it returns all tweets that have at least one TokenDetails (similar to GetTweets)
func (d *Database) GetTweetsByTags(limit, offset int, tags []string) ([]*Tweet, error) {
	var tweets []*Tweet

	// Base query with preloads
	query := d.Preload("User").
		Preload("ExtractedCAs").
		Preload("ExtractedCAs.TokenDetails").
		Preload("Tags").
		// Join with recognized_cas and token_details to ensure at least one token_details exists
		Joins("INNER JOIN tweet_contract_addresses ON tweets.id = tweet_contract_addresses.tweet_id").
		Joins("INNER JOIN recognized_cas ON tweet_contract_addresses.recognized_ca_id = recognized_cas.id").
		Joins("INNER JOIN token_details ON recognized_cas.ca_address = token_details.ca_address_fk").
		Where("contains_target_keyword = ?", true)

	// If tags are provided, filter by them
	if len(tags) > 0 {
		// Join tweet_tags and tags tables
		query = query.Joins("INNER JOIN tweet_tags ON tweets.id = tweet_tags.tweet_id").
			Joins("INNER JOIN tags ON tweet_tags.tag_id = tags.id").
			Where("tags.name IN ?", tags)
	}

	// Group by tweets.id to avoid duplicates (needed in both cases now)
	query = query.Group("tweets.id")

	// Finalize the query with order, limit, offset
	err := query.Order("published_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tweets).Error

	if err != nil {
		return nil, err
	}

	// Populate collection tags
	if err := d.PopulateCollectionTags(tweets); err != nil {
		log.Error().Err(err).Msg("Failed to populate collection tags")
		// Don't return error, just log it as this is not critical
	}

	return tweets, nil
}

// GetTokenDetails retrieves all token details for a CA
func (d *Database) GetTokenDetails(caAddress string) ([]*TokenDetails, error) {
	var tokenDetails []*TokenDetails

	// Use GORM's Find method to find all token details for the CA
	err := d.Where("ca_address_fk = ?", caAddress).Find(&tokenDetails).Error

	if err != nil {
		return nil, err
	}

	return tokenDetails, nil
}

// GetTokenDetailsBySource retrieves token details for a CA from a specific source
func (d *Database) GetTokenDetailsBySource(caAddress string, source string) (*TokenDetails, error) {
	tokenDetails := &TokenDetails{}

	// Use GORM's First method to find the token details for the specific source
	err := d.Where("ca_address_fk = ? AND source = ?", caAddress, source).First(tokenDetails).Error

	if err != nil {
		return nil, err
	}

	return tokenDetails, nil
}

// IsCARecognized checks if a CA is recognized
func (d *Database) IsCARecognized(caAddress string) (bool, string, error) {
	var recognizedCA RecognizedCA

	// Use GORM to find the recognized CA
	err := d.Where("ca_address = ?", caAddress).First(&recognizedCA).Error

	// If record not found, return false
	if err == gorm.ErrRecordNotFound {
		return false, "", nil
	}

	if err != nil {
		return false, "", err
	}

	return true, recognizedCA.ChainType, nil
}

// GetRecognizedCAsForUpdate retrieves recognized CAs that need token data update
func (d *Database) GetRecognizedCAsForUpdate(limit int) ([]*RecognizedCA, error) {
	var recognizedCAs []*RecognizedCA

	// Use GORM to find CAs that need updating
	threshold := time.Now().Add(-100 * time.Minute)
	err := d.Where("last_checked_for_data_at IS NULL OR last_checked_for_data_at < ?", threshold).
		Order("last_tweet_at DESC").
		Order("last_checked_for_data_at NULLS FIRST").
		Limit(limit).
		Find(&recognizedCAs).Error

	if err != nil {
		return nil, err
	}

	return recognizedCAs, nil
}

// DeleteRecognizedCA deletes a recognized CA
func (d *Database) DeleteRecognizedCA(caAddress string) error {
	// Use GORM to delete the recognized CA
	return d.Delete(&RecognizedCA{}, "ca_address = ?", caAddress).Error
}

// IncrementCAReference increments the reference count for a recognized CA
func (d *Database) IncrementCAReference(caAddress string, chainID string, tokenNameHint string, tweetTime time.Time, tagNames []string) error {
	// Start a transaction
	return d.Transaction(func(tx *gorm.DB) error {
		// Try to find the existing recognized CA
		var recognizedCA RecognizedCA
		err := tx.Where("ca_address = ?", caAddress).First(&recognizedCA).Error

		var latestTime = tweetTime

		// If not found, create a new one with reference count 1
		if err == gorm.ErrRecordNotFound {
			// Create the tags first
			tags, err := d.GetOrCreateTags(tagNames)
			if err != nil {
				return err
			}

			newCA := &RecognizedCA{
				CAAddress:      caAddress,
				ChainType:      chainID,
				TokenNameHint:  tokenNameHint,
				LastTweetAt:    latestTime,
				AddedAt:        time.Now(),
				ReferenceCount: 1,
			}

			if err := tx.Create(newCA).Error; err != nil {
				return err
			}

			// Associate with tags
			if len(tags) > 0 {
				if err := d.AssociateCAWithTags(newCA.ID, tags); err != nil {
					return err
				}
			}

			return nil
		} else if err != nil {
			return err
		}

		if recognizedCA.LastTweetAt.After(latestTime) {
			latestTime = recognizedCA.LastTweetAt
		}

		// Update the reference count
		if err := tx.Model(&RecognizedCA{}).
			Where("ca_address = ?", caAddress).
			Update("last_tweet_at", latestTime).
			Update("reference_count", gorm.Expr("reference_count + 1")).Error; err != nil {
			return err
		}

		// Add new tags if provided
		if len(tagNames) > 0 {
			// Get or create the tags
			newTags, err := d.GetOrCreateTags(tagNames)
			if err != nil {
				return err
			}

			// Associate with CA
			if err := d.AssociateCAWithTags(recognizedCA.ID, newTags); err != nil {
				return err
			}
		}

		return nil
	})
}

// GetLatestTweetID gets the ID of the most recent tweet
func (d *Database) GetLatestTweetID() (string, error) {
	var tweet Tweet

	// Use GORM to find the most recent tweet
	err := d.Order("published_at DESC").Limit(1).First(&tweet).Error

	// If no tweets found, return empty string
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return "", nil
	}

	if err != nil {
		return "", err
	}

	return tweet.TweetID, nil
}

// TweetExists checks if a tweet ID already exists in the database
func (d *Database) TweetExists(tweetID string) (bool, error) {
	var tweet Tweet

	// Use GORM to check for the tweet
	err := d.Where("tweet_id = ?", tweetID).First(&tweet).Error

	// If record not found, return false
	if err == gorm.ErrRecordNotFound {
		return false, nil
	}

	if err != nil {
		return false, err
	}

	return true, nil
}

func (d *Database) TweetExistsWithTweet(tweetID string) (*Tweet, bool, error) {
	var tweet Tweet

	// Use GORM to check for the tweet
	err := d.Where("tweet_id = ?", tweetID).First(&tweet).Error

	// If record not found, return false
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, false, nil
	}

	if err != nil {
		return nil, false, err
	}

	return &tweet, true, nil
}

// GetRecognizedCAsWithTweets retrieves recognized CAs with their associated tweets, only including CAs with TokenDetails
func (d *Database) GetRecognizedCAsWithTweets(limit, offset int) ([]*RecognizedCA, error) {
	var recognizedCAs []*RecognizedCA

	// Query recognized CAs with their token details and tweets
	// Only include CAs that have at least one TokenDetails entry
	err := d.Preload("TokenDetails").
		Preload("Tweets.User").
		Where("EXISTS (SELECT 1 FROM token_details td WHERE td.ca_address_fk = recognized_cas.ca_address)").
		Order("last_tweet_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&recognizedCAs).Error

	if err != nil {
		return nil, err
	}

	// Populate collection tags for all tweets
	if err := d.PopulateCollectionTagsForCAs(recognizedCAs); err != nil {
		log.Error().Err(err).Msg("Failed to populate collection tags for CAs")
		// Don't return error, just log it as this is not critical
	}

	for _, k := range recognizedCAs {
		// Sort each CA's Tweets by PublishedAt in descending order (newest first)
		if len(k.Tweets) > 1 {
			sort.Slice(k.Tweets, func(i, j int) bool {
				return k.Tweets[i].PublishedAt.After(k.Tweets[j].PublishedAt)
			})
		}
	}

	return recognizedCAs, nil
}

// GetRecognizedCAsByTags retrieves recognized CAs with their associated tweets, filtered by tags
// If tags is empty, it returns all CAs (same as GetRecognizedCAsWithTweets)
func (d *Database) GetRecognizedCAsByTags(limit, offset int, tags []string) ([]*RecognizedCA, error) {
	var recognizedCAs []*RecognizedCA

	// Base query with preloads
	query := d.Preload("TokenDetails").
		Preload("Tweets.User").
		Preload("Tags")

	// If tags are provided, filter by them
	if len(tags) > 0 {
		// We need to find CAs that have ANY of the requested tags (OR logic)
		// Build a single subquery with multiple OR conditions
		var tagConditions []string
		for _, tag := range tags {
			tagConditions = append(tagConditions, fmt.Sprintf("t.name = '%s'", tag))
		}

		// Combine all tag conditions with OR
		tagCondition := fmt.Sprintf(
			"EXISTS (SELECT 1 FROM recognized_ca_tags rct JOIN tags t ON rct.tag_id = t.id WHERE rct.recognized_ca_id = recognized_cas.id AND (%s))",
			strings.Join(tagConditions, " OR "),
		)
		query = query.Where(tagCondition)
	}

	// Only include CAs that have at least one TokenDetails entry
	query = query.Where("EXISTS (SELECT 1 FROM token_details td WHERE td.ca_address_fk = recognized_cas.ca_address)")

	// Finalize the query with order, limit, offset
	err := query.Order("last_tweet_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&recognizedCAs).Error

	if err != nil {
		return nil, err
	}

	// Populate collection tags for all tweets
	if err := d.PopulateCollectionTagsForCAs(recognizedCAs); err != nil {
		log.Error().Err(err).Msg("Failed to populate collection tags for CAs")
		// Don't return error, just log it as this is not critical
	}

	for _, k := range recognizedCAs {
		// Sort each CA's Tweets by PublishedAt in descending order (newest first)
		if len(k.Tweets) > 1 {
			sort.Slice(k.Tweets, func(i, j int) bool {
				return k.Tweets[i].PublishedAt.After(k.Tweets[j].PublishedAt)
			})
		}
	}

	return recognizedCAs, nil
}

// GetRecognizedCAByAddressAndChainID retrieves a single recognized CA by address and chain ID
func (d *Database) GetRecognizedCAByAddressAndChainID(caAddress, chainID string) (*RecognizedCA, error) {
	var recognizedCA RecognizedCA

	// Query recognized CA with token details and tweets
	// Match RecognizedCA by address and ensure it has TokenDetails with the matching chain_id
	err := d.Preload("TokenDetails", "chain_id = ?", chainID).
		Preload("Tweets.User").
		Preload("Tags").
		Where("ca_address = ?", caAddress).
		Where("EXISTS (SELECT 1 FROM token_details td WHERE td.ca_address_fk = recognized_cas.ca_address AND td.chain_id = ?)", chainID).
		First(&recognizedCA).Error

	if err != nil {
		return nil, err
	}

	// Sort Tweets for CA in PublishedAt descending order (new first)
	if len(recognizedCA.Tweets) > 1 {
		sort.Slice(recognizedCA.Tweets, func(i, j int) bool {
			return recognizedCA.Tweets[i].PublishedAt.After(recognizedCA.Tweets[j].PublishedAt)
		})
	}

	return &recognizedCA, nil
}

// CheckTelegramNotificationStatus checks if a Telegram notification has been sent for a tweet
func (d *Database) CheckTelegramNotificationStatus(tweetID string) (bool, error) {
	var tweet Tweet
	err := d.Where("tweet_id = ?", tweetID).Select("telegram_notification_sent").First(&tweet).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, fmt.Errorf("tweet not found: %s", tweetID)
		}
		return false, fmt.Errorf("failed to check notification status: %w", err)
	}
	return tweet.TelegramNotificationSent, nil
}

// MarkTelegramNotificationSent marks a tweet as having had its Telegram notification sent
func (d *Database) MarkTelegramNotificationSent(tweetID string) error {
	now := time.Now()
	result := d.Exec(`
		UPDATE tweets
		SET telegram_notification_sent = ?, telegram_notification_sent_at = ?
		WHERE tweet_id = ?
	`, true, &now, tweetID)

	if result.Error != nil {
		return fmt.Errorf("failed to mark notification as sent: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no tweet found with ID: %s", tweetID)
	}

	return nil
}

// GetTelegramNotificationStats returns statistics about Telegram notifications
func (d *Database) GetTelegramNotificationStats() (map[string]interface{}, error) {
	var stats struct {
		TotalTweets            int64 `json:"total_tweets"`
		NotificationsSent      int64 `json:"notifications_sent"`
		NotificationsNotSent   int64 `json:"notifications_not_sent"`
		NotificationsSentToday int64 `json:"notifications_sent_today"`
	}

	// Get total tweets
	if err := d.Model(&Tweet{}).Count(&stats.TotalTweets).Error; err != nil {
		return nil, fmt.Errorf("failed to count total tweets: %w", err)
	}

	// Get notifications sent
	if err := d.Model(&Tweet{}).Where("telegram_notification_sent = ?", true).Count(&stats.NotificationsSent).Error; err != nil {
		return nil, fmt.Errorf("failed to count notifications sent: %w", err)
	}

	// Get notifications not sent
	if err := d.Model(&Tweet{}).Where("telegram_notification_sent = ?", false).Count(&stats.NotificationsNotSent).Error; err != nil {
		return nil, fmt.Errorf("failed to count notifications not sent: %w", err)
	}

	// Get notifications sent today
	today := time.Now().Truncate(24 * time.Hour)
	if err := d.Model(&Tweet{}).Where("telegram_notification_sent = ? AND telegram_notification_sent_at >= ?", true, today).Count(&stats.NotificationsSentToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count notifications sent today: %w", err)
	}

	result := map[string]interface{}{
		"total_tweets":             stats.TotalTweets,
		"notifications_sent":       stats.NotificationsSent,
		"notifications_not_sent":   stats.NotificationsNotSent,
		"notifications_sent_today": stats.NotificationsSentToday,
		"notification_rate":        float64(stats.NotificationsSent) / float64(stats.TotalTweets) * 100,
	}

	return result, nil
}

// GetTweetByID retrieves a single tweet by its ID
func (d *Database) GetTweetByID(tweetID string) (*Tweet, error) {
	var tweet Tweet

	// Query tweet with user and associated contract addresses
	err := d.Preload("User").
		Preload("ExtractedCAs.TokenDetails").
		Preload("Tags").
		Where("tweet_id = ?", tweetID).
		First(&tweet).Error

	if err != nil {
		return nil, err
	}

	// Populate collection tags
	tweets := []*Tweet{&tweet}
	if err := d.PopulateCollectionTags(tweets); err != nil {
		log.Error().Err(err).Msg("Failed to populate collection tags")
		// Don't return error, just log it as this is not critical
	}

	return &tweet, nil
}

// TwitterUserAnnouncementStats represents the announcement statistics for a specific Twitter user
type TwitterUserAnnouncementStats struct {
	UserID                    string `json:"user_id"`
	ScreenName                string `json:"screen_name"`
	Name                      string `json:"name"`
	ProfileImageURL           string `json:"profile_image_url"`
	ProductUpdatesCount       int64  `json:"product_updates_count"`
	BusinessDataCount         int64  `json:"business_data_count"`
	EcosystemPartnershipCount int64  `json:"ecosystem_partnership_count"`
	ProfitOpportunityCount    int64  `json:"profit_opportunity_count"`
	IndustryEventsCount       int64  `json:"industry_events_count"`
	OthersCount               int64  `json:"others_count"`
	TotalAnnouncementsCount   int64  `json:"total_announcements_count"`
}

// GetAnnouncementStatistics retrieves announcement statistics grouped by Twitter user for tweets with source_list_type="Projects" and ai_judgment="YES"
// Optional time filtering can be applied using startTime and endTime parameters (Unix timestamps)
func (d *Database) GetAnnouncementStatistics(startTime, endTime *int64) ([]TwitterUserAnnouncementStats, error) {
	var userStats []TwitterUserAnnouncementStats

	// Build the query with base conditions
	query := d.Model(&Tweet{}).
		Select(`
			tweets.user_id_fk as user_id,
			twitter_users.screen_name,
			twitter_users.name,
			twitter_users.profile_image_url,
			SUM(CASE WHEN is_product_update = true THEN 1 ELSE 0 END) as product_updates_count,
			SUM(CASE WHEN is_business_data = true THEN 1 ELSE 0 END) as business_data_count,
			SUM(CASE WHEN is_ecosystem_partnership = true THEN 1 ELSE 0 END) as ecosystem_partnership_count,
			SUM(CASE WHEN is_profit_opportunity = true THEN 1 ELSE 0 END) as profit_opportunity_count,
			SUM(CASE WHEN is_others = true THEN 1 ELSE 0 END) as others_count,
			SUM(CASE WHEN is_industry_event = true THEN 1 ELSE 0 END) as industry_events_count,
			COUNT(*) as total_announcements_count
		`).
		Joins("INNER JOIN twitter_users ON tweets.user_id_fk = twitter_users.user_id").
		Where("tweets.source_list_type = ?", "Projects").
		Where("tweets.ai_judgment = ?", "YES")

	// Add time filtering if both parameters are provided
	if startTime != nil && endTime != nil {
		startTimeFormatted := time.Unix(*startTime, 0).UTC()
		endTimeFormatted := time.Unix(*endTime, 0).UTC()
		query = query.Where("tweets.published_at >= ? AND tweets.published_at <= ?", startTimeFormatted, endTimeFormatted)
	}

	// Complete the query
	err := query.
		Group("tweets.user_id_fk, twitter_users.screen_name, twitter_users.name, twitter_users.profile_image_url").
		Order("total_announcements_count DESC").
		Scan(&userStats).Error

	if err != nil {
		return nil, err
	}

	return userStats, nil
}

// SaveCollectionTags saves collection tags data to the database
// This method will clear existing data and insert new data for the given collections
func (d *Database) SaveCollectionTags(collections []interface{}) error {
	return d.Transaction(func(tx *gorm.DB) error {
		// Prepare collection tags for batch insert
		var collectionTags []CollectionTag
		for _, collection := range collections {
			// Type assertion to access the collection data
			if collectionMap, ok := collection.(map[string]interface{}); ok {
				twitterUsername, _ := collectionMap["twitter_username"].(string)
				if twitterUsername == "" {
					continue
				}

				// Extract tags array
				if tagsInterface, exists := collectionMap["tags"]; exists {
					if tagsArray, ok := tagsInterface.([]interface{}); ok {
						for _, tagInterface := range tagsArray {
							if tagMap, ok := tagInterface.(map[string]interface{}); ok {
								tagName, _ := tagMap["name"].(string)

								if tagName != "" {
									collectionTag := CollectionTag{
										TagName:         tagName,
										TwitterUsername: twitterUsername,
									}
									collectionTags = append(collectionTags, collectionTag)
								}
							}
						}
					}
				}
			}
		}

		// Batch insert collection tags
		if len(collectionTags) > 0 {
			// 构建新数据的映射，用于快速查找
			newTagsMap := make(map[string]map[string]bool)
			var allNewUsernames []string
			usernamesSet := make(map[string]bool)

			// 整理新数据，建立用户名到标签集合的映射
			for _, tag := range collectionTags {
				if !usernamesSet[tag.TwitterUsername] {
					usernamesSet[tag.TwitterUsername] = true
					allNewUsernames = append(allNewUsernames, tag.TwitterUsername)
				}

				if _, exists := newTagsMap[tag.TwitterUsername]; !exists {
					newTagsMap[tag.TwitterUsername] = make(map[string]bool)
				}
				newTagsMap[tag.TwitterUsername][tag.TagName] = true
			}

			// 开始事务内的原子操作
			// 1. 删除不在新数据中的用户的所有标签
			if err := tx.Where("twitter_username NOT IN ?", allNewUsernames).Delete(&CollectionTag{}).Error; err != nil {
				return fmt.Errorf("failed to delete tags for removed users: %w", err)
			}

			// 2. 对于保留的用户，获取其现有标签
			var existingTags []struct {
				TwitterUsername string
				TagName         string
			}
			if err := tx.Model(&CollectionTag{}).Where("twitter_username IN ?", allNewUsernames).
				Select("twitter_username, tag_name").Find(&existingTags).Error; err != nil {
				return fmt.Errorf("failed to get existing tags: %w", err)
			}

			// 3. 找出需要删除的标签（用户存在但标签不在新集合中）
			var tagsToDelete []CollectionTag
			for _, existing := range existingTags {
				if userTags, ok := newTagsMap[existing.TwitterUsername]; ok {
					if !userTags[existing.TagName] {
						// 这个标签不再存在于新数据中
						tagsToDelete = append(tagsToDelete, CollectionTag{
							TwitterUsername: existing.TwitterUsername,
							TagName:         existing.TagName,
						})
					}
				}
			}

			// 4. 删除不需要的标签
			if len(tagsToDelete) > 0 {
				for _, tag := range tagsToDelete {
					if err := tx.Where("twitter_username = ? AND tag_name = ?",
						tag.TwitterUsername, tag.TagName).Delete(&CollectionTag{}).Error; err != nil {
						return fmt.Errorf("failed to delete obsolete tags: %w", err)
					}
				}
			}

			// 5. 使用upsert插入新标签（避免重复）
			if err := tx.Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "twitter_username"}, {Name: "tag_name"}},
				DoNothing: true,
			}).CreateInBatches(collectionTags, 111).Error; err != nil {
				return fmt.Errorf("failed to save collection tags: %w", err)
			}
		}

		return nil
	})
}

// GetTagsByTwitterUsername retrieves all tags for a given Twitter username from collection_tags table
func (d *Database) GetTagsByTwitterUsername(twitterUsername string) ([]string, error) {
	var collectionTags []CollectionTag
	err := d.Where("twitter_username = ?", twitterUsername).Find(&collectionTags).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get tags for twitter username %s: %w", twitterUsername, err)
	}

	// Extract unique tag names
	tagSet := make(map[string]bool)
	for _, tag := range collectionTags {
		tagSet[tag.TagName] = true
	}

	var tags []string
	for tagName := range tagSet {
		tags = append(tags, tagName)
	}

	return tags, nil
}

// GetTagsByTwitterUsernames retrieves all tags for multiple Twitter usernames in a single query
func (d *Database) GetTagsByTwitterUsernames(twitterUsernames []string) (map[string][]string, error) {
	if len(twitterUsernames) == 0 {
		return make(map[string][]string), nil
	}

	var collectionTags []CollectionTag
	err := d.Where("twitter_username IN ?", twitterUsernames).Find(&collectionTags).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get tags for twitter usernames: %w", err)
	}

	// Group tags by username
	result := make(map[string][]string)
	userTagSets := make(map[string]map[string]bool)

	// Initialize maps for all usernames
	for _, username := range twitterUsernames {
		userTagSets[username] = make(map[string]bool)
		result[username] = []string{}
	}

	// Group and deduplicate tags by username
	for _, tag := range collectionTags {
		if tagSet, exists := userTagSets[tag.TwitterUsername]; exists {
			if !tagSet[tag.TagName] {
				tagSet[tag.TagName] = true
				result[tag.TwitterUsername] = append(result[tag.TwitterUsername], tag.TagName)
			}
		}
	}

	return result, nil
}

// PopulateCollectionTags fills the CollectionTags field for a slice of tweets
func (d *Database) PopulateCollectionTags(tweets []*Tweet) error {
	if len(tweets) == 0 {
		return nil
	}

	// Extract unique screen names
	screenNameSet := make(map[string]bool)
	var screenNames []string

	for _, tweet := range tweets {
		if tweet.User != nil && tweet.User.ScreenName != "" {
			if !screenNameSet[tweet.User.ScreenName] {
				screenNameSet[tweet.User.ScreenName] = true
				screenNames = append(screenNames, tweet.User.ScreenName)
			}
		}
	}

	if len(screenNames) == 0 {
		return nil
	}

	// Batch query collection tags
	collectionTagsMap, err := d.GetTagsByTwitterUsernames(screenNames)
	if err != nil {
		return fmt.Errorf("failed to get collection tags: %w", err)
	}

	// Populate collection tags for each tweet
	for _, tweet := range tweets {
		if tweet.User != nil && tweet.User.ScreenName != "" {
			if tags, exists := collectionTagsMap[tweet.User.ScreenName]; exists {
				tweet.CollectionTags = tags
			}
		}
	}

	return nil
}

// PopulateCollectionTagsForCAs fills the CollectionTags field for tweets in a slice of RecognizedCAs
func (d *Database) PopulateCollectionTagsForCAs(recognizedCAs []*RecognizedCA) error {
	// Collect all tweets from all CAs
	var allTweets []*Tweet
	for _, ca := range recognizedCAs {
		for i := range ca.Tweets {
			allTweets = append(allTweets, &ca.Tweets[i])
		}
	}

	// Populate collection tags for all tweets
	return d.PopulateCollectionTags(allTweets)
}

// GetUserRecentTweets gets user's recent tweets for duplicate detection
func (d *Database) GetUserRecentTweets(userID string, excludeTweetID string, timeWindow time.Time, limit int) ([]Tweet, error) {
	var tweets []Tweet

	query := d.Where("user_id_fk = ? AND tweet_id != ? AND published_at >= ? AND is_outdated = ?",
		userID, excludeTweetID, timeWindow, false).
		Order("published_at DESC").
		Limit(limit)

	if err := query.Find(&tweets).Error; err != nil {
		return nil, err
	}

	return tweets, nil
}

// MarkTweetsAsOutdated marks multiple tweets as outdated in a transaction
func (d *Database) MarkTweetsAsOutdated(tweetIDs []string, newTweetID string) error {
	now := time.Now()

	return d.Transaction(func(tx *gorm.DB) error {
		for _, tweetID := range tweetIDs {
			if err := tx.Model(&Tweet{}).
				Where("tweet_id = ?", tweetID).
				Updates(map[string]interface{}{
					"is_outdated":          true,
					"outdated_at":          now,
					"outdated_by_tweet_id": newTweetID,
				}).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
