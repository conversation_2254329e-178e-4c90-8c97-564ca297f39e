package config

import (
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/BurntSushi/toml"
)

// CommandLineFlags holds command-line flag values
type CommandLineFlags struct {
	OnlyAPI    *bool
	EnableHttp *bool
	LogLevel   *string
}

// Config holds all configuration for the application
type Config struct {
	Server      ServerConfig
	Database    DatabaseConfig
	SocialData  SocialDataConfig
	DexScreener DexScreenerConfig
	Moralis     MoralisConfig
	Logging     LoggingConfig
	Metrics     MetricsConfig
	AI          AIConfig
	Telegram    TelegramConfig
	Redis       RedisConfig
}

// ServerConfig holds server-specific configuration
type ServerConfig struct {
	Address         string
	ReadTimeout     time.Duration
	WriteTimeout    time.Duration
	ShutdownTimeout time.Duration
	OnlyAPI         bool
	GinMode         string
	Enable          bool
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string
	Pretty bool
}

// MetricsConfig holds metrics configuration
type MetricsConfig struct {
	Enabled bool
	Path    string
}

// TelegramConfig holds Telegram bot configuration
type TelegramConfig struct {
	BotToken string
	ChatID   string
	Enabled  bool
}

type AIConfig struct {
	Enabled        bool
	APIKey         string
	Model          string
	BaseURL        string
	MaxRetry       int
	RequestsPerSec int // Rate limit for API requests per minute
}

// RedisConfig holds Redis-specific configuration
type RedisConfig struct {
	Host               string
	Port               int
	Password           string
	DB                 int
	PoolSize           int
	MinIdleConns       int
	MaxRetries         int
	DialTimeout        time.Duration
	ReadTimeout        time.Duration
	WriteTimeout       time.Duration
	PoolTimeout        time.Duration
	IdleTimeout        time.Duration
	IdleCheckFrequency time.Duration
	// Distributed locking configuration
	LockTimeout         time.Duration // Default lock timeout
	LockRetryDelay      time.Duration // Delay between lock acquisition retries
	LockMaxRetries      int           // Maximum number of lock acquisition retries
	LockRefreshInterval time.Duration // Interval for lock refresh/extension
	LockCleanupTimeout  time.Duration // Timeout for cleanup operations during shutdown
	// Key management configuration
	KeyPrefix      string   // Prefix for all Redis keys (e.g., "v2_")
	CleanupOldKeys bool     // Whether to cleanup old keys on startup
	OldKeyPatterns []string // Patterns of old keys to cleanup
}

// DatabaseConfig holds database-specific configuration
type DatabaseConfig struct {
	Host             string
	Port             int
	User             string
	Password         string
	DBName           string
	SSLMode          string
	MigrateOnStartup bool
}

// SocialDataConfig holds SocialData.tools API configuration
type SocialDataConfig struct {
	APIKey                       string
	BaseURL                      string
	SearchKeywords               []string
	PollingInterval              time.Duration
	RequestsPerSec               int
	WebhookEnabled               bool
	WebhookEndpoint              string
	WebhookSecretKey             string
	InitialLookbackHours         int           // Initial lookback hours for first tweet fetch, 0 means nil lookback
	BlacklistedUsers             []string      // List of user screen names to ignore when processing tweets
	TwitterLists                 []TwitterList // List of Twitter lists to fetch tweets from
	PeriodicUpdateEnabled        bool          `toml:"periodic_update_enabled"`
	PeriodicUpdateRunOnceOnly    bool          `toml:"periodic_update_run_once_only"`
	PeriodicUpdateIntervalHours  int           `toml:"periodic_update_interval_hours"`
	PeriodicUpdateNewerThanHours int           `toml:"periodic_update_newer_than_hours"`
	PeriodicUpdateOlderThanHours int           `toml:"periodic_update_older_than_hours"`
}

// TwitterList represents a Twitter list configuration
type TwitterList struct {
	ID   string
	Type string
}

// DexScreenerConfig holds DexScreener API configuration
type DexScreenerConfig struct {
	BaseURL         string
	RequestsPerSec  int
	UpdateInterval  time.Duration
	SupportedChains []string
}

// MoralisConfig holds Moralis API configuration
type MoralisConfig struct {
	APIKey         string
	RequestsPerSec int
}

// TomlConfig is the root structure for TOML configuration
type TomlConfig struct {
	Server      TomlServerConfig      `toml:"server"`
	Logging     TomlLoggingConfig     `toml:"logging"`
	Metrics     TomlMetricsConfig     `toml:"metrics"`
	Database    TomlDatabaseConfig    `toml:"database"`
	SocialData  TomlSocialDataConfig  `toml:"social_data"`
	DexScreener TomlDexScreenerConfig `toml:"dex_screener"`
	Moralis     TomlMoralisConfig     `toml:"moralis"`
	AI          TomlAIConfig          `toml:"ai"`
	Telegram    TomlTelegramConfig    `toml:"telegram"`
	Redis       TomlRedisConfig       `toml:"redis"`
}

// TOML config structures
type TomlServerConfig struct {
	Address            string `toml:"address"`
	ReadTimeoutSec     int    `toml:"read_timeout_sec"`
	WriteTimeoutSec    int    `toml:"write_timeout_sec"`
	ShutdownTimeoutSec int    `toml:"shutdown_timeout_sec"`
	OnlyAPI            bool   `toml:"only_api"`
	GinMode            string `toml:"gin_mode"`
	Enable             bool   `toml:"enable"`
}

type TomlLoggingConfig struct {
	Level  string `toml:"level"`
	Pretty bool   `toml:"pretty"`
}

type TomlMetricsConfig struct {
	Enabled bool   `toml:"enabled"`
	Path    string `toml:"path"`
}

type TomlDatabaseConfig struct {
	Host             string `toml:"host"`
	Port             int    `toml:"port"`
	User             string `toml:"user"`
	Password         string `toml:"password"`
	DBName           string `toml:"db_name"`
	SSLMode          string `toml:"ssl_mode"`
	MigrateOnStartup bool   `toml:"migrate_on_startup"`
}

type TomlSocialDataConfig struct {
	APIKey               string            `toml:"api_key"`
	BaseURL              string            `toml:"base_url"`
	SearchKeywords       []string          `toml:"search_keywords"`
	PollingIntervalSec   int               `toml:"polling_interval_sec"`
	RequestsPerSec       int               `toml:"requests_per_sec"`
	WebhookEnabled       bool              `toml:"webhook_enabled"`
	WebhookEndpoint      string            `toml:"webhook_endpoint"`
	WebhookSecretKey     string            `toml:"webhook_secret_key"`
	InitialLookbackHours int               `toml:"initial_lookback_hours"`
	BlacklistedUsers     []string          `toml:"blacklisted_users"`
	TwitterLists         []TomlTwitterList `toml:"twitter_lists"`

	PeriodicUpdateEnabled        bool `toml:"periodic_update_enabled"`
	PeriodicUpdateIntervalHours  int  `toml:"periodic_update_interval_hours"`
	PeriodicUpdateNewerThanHours int  `toml:"periodic_update_newer_than_hours"`
	PeriodicUpdateOlderThanHours int  `toml:"periodic_update_older_than_hours"`
}

// TomlTwitterList represents a Twitter list configuration in TOML format
type TomlTwitterList struct {
	ID   string `toml:"id"`
	Type string `toml:"type"`
}

type TomlDexScreenerConfig struct {
	BaseURL           string `toml:"base_url"`
	RequestsPerSec    int    `toml:"requests_per_sec"`
	UpdateIntervalMin int    `toml:"update_interval_min"`
	SupportedChains   string `toml:"supported_chains"`
}

type TomlMoralisConfig struct {
	APIKey         string `toml:"api_key"`
	BaseURL        string `toml:"base_url"`
	RequestsPerSec int    `toml:"requests_per_sec"`
}

// TomlTelegramConfig is the TOML structure for Telegram configuration
type TomlTelegramConfig struct {
	BotToken string `toml:"bot_token"`
	ChatID   string `toml:"chat_id"`
	Enabled  bool   `toml:"enabled"`
}

type TomlAIConfig struct {
	Enabled        bool   `toml:"enabled"`
	APIKey         string `toml:"api_key"`
	Model          string `toml:"model"`
	BaseURL        string `toml:"base_url"`
	MaxRetry       int    `toml:"max_retry"`
	RequestsPerSec int    `toml:"requests_per_sec"`
}

// TomlRedisConfig is the TOML structure for Redis configuration
type TomlRedisConfig struct {
	Host                  string `toml:"host"`
	Port                  int    `toml:"port"`
	Password              string `toml:"password"`
	DB                    int    `toml:"db"`
	PoolSize              int    `toml:"pool_size"`
	MinIdleConns          int    `toml:"min_idle_conns"`
	MaxRetries            int    `toml:"max_retries"`
	DialTimeoutSec        int    `toml:"dial_timeout_sec"`
	ReadTimeoutSec        int    `toml:"read_timeout_sec"`
	WriteTimeoutSec       int    `toml:"write_timeout_sec"`
	PoolTimeoutSec        int    `toml:"pool_timeout_sec"`
	IdleTimeoutSec        int    `toml:"idle_timeout_sec"`
	IdleCheckFrequencySec int    `toml:"idle_check_frequency_sec"`
	// Distributed locking configuration
	LockTimeoutSec         int `toml:"lock_timeout_sec"`
	LockRetryDelaySec      int `toml:"lock_retry_delay_sec"`
	LockMaxRetries         int `toml:"lock_max_retries"`
	LockRefreshIntervalSec int `toml:"lock_refresh_interval_sec"`
	LockCleanupTimeoutSec  int `toml:"lock_cleanup_timeout_sec"`
	// Key management configuration
	KeyPrefix      string   `toml:"key_prefix"`
	CleanupOldKeys bool     `toml:"cleanup_old_keys"`
	OldKeyPatterns []string `toml:"old_key_patterns"`
}

// convertTomlTwitterLists converts a slice of TomlTwitterList to a slice of TwitterList
func convertTomlTwitterLists(tomlLists []TomlTwitterList) []TwitterList {
	twitterLists := make([]TwitterList, len(tomlLists))
	for i, tomlList := range tomlLists {
		twitterLists[i] = TwitterList{
			ID:   tomlList.ID,
			Type: tomlList.Type,
		}
	}
	return twitterLists
}

// ParseCommandLineFlags parses command-line flags
func ParseCommandLineFlags() *CommandLineFlags {
	flags := &CommandLineFlags{
		OnlyAPI:    flag.Bool("only-api", false, "Only start API server without background workers"),
		EnableHttp: flag.Bool("enable-http", false, "Enable HTTP server"),
		LogLevel:   flag.String("log-level", "", "Log level (debug, info, warn, error, fatal, panic)"),
	}
	flag.Parse()
	return flags
}

// LoadWithFlags loads configuration from a TOML file and merges with command-line flags
func LoadWithFlags(flags *CommandLineFlags) (*Config, error) {
	configPath := getConfigPath()

	var tomlConfig TomlConfig
	_, err := toml.DecodeFile(configPath, &tomlConfig)
	if err != nil {
		return nil, fmt.Errorf("error decoding TOML config file: %w", err)
	}

	// Validate required fields
	if tomlConfig.SocialData.APIKey == "" || tomlConfig.SocialData.APIKey == "your_socialdata_api_key_here" {
		return nil, fmt.Errorf("social_data.api_key is required in config file")
	}

	// Parse supported chains
	supportedChains := []string{"ethereum", "bsc", "polygon", "solana"}
	if tomlConfig.DexScreener.SupportedChains != "" {
		supportedChains = strings.Split(tomlConfig.DexScreener.SupportedChains, ",")
	}

	// Merge command-line flags with TOML config (flags have higher priority)
	serverOnlyAPI := tomlConfig.Server.OnlyAPI
	if flags != nil && flags.OnlyAPI != nil {
		// Check if the flag was actually set by the user
		flagSet := false
		flag.Visit(func(f *flag.Flag) {
			if f.Name == "only-api" {
				flagSet = true
			}
		})
		if flagSet {
			serverOnlyAPI = *flags.OnlyAPI
		}
	}

	serverEnable := tomlConfig.Server.Enable
	if flags != nil && flags.EnableHttp != nil {
		// Check if the flag was actually set by the user
		flagSet := false
		flag.Visit(func(f *flag.Flag) {
			if f.Name == "enable-http" {
				flagSet = true
			}
		})
		if flagSet {
			serverEnable = *flags.EnableHttp
		}
	}

	loggingLevel := tomlConfig.Logging.Level
	if flags != nil && flags.LogLevel != nil && *flags.LogLevel != "" {
		loggingLevel = *flags.LogLevel
	}

	return &Config{
		Server: ServerConfig{
			Address:         tomlConfig.Server.Address,
			ReadTimeout:     time.Duration(tomlConfig.Server.ReadTimeoutSec) * time.Second,
			WriteTimeout:    time.Duration(tomlConfig.Server.WriteTimeoutSec) * time.Second,
			ShutdownTimeout: time.Duration(tomlConfig.Server.ShutdownTimeoutSec) * time.Second,
			OnlyAPI:         serverOnlyAPI,
			GinMode:         tomlConfig.Server.GinMode,
			Enable:          serverEnable,
		},
		Logging: LoggingConfig{
			Level:  loggingLevel,
			Pretty: tomlConfig.Logging.Pretty,
		},
		Metrics: MetricsConfig{
			Enabled: tomlConfig.Metrics.Enabled,
			Path:    tomlConfig.Metrics.Path,
		},
		Moralis: MoralisConfig{
			APIKey:         tomlConfig.Moralis.APIKey,
			RequestsPerSec: tomlConfig.Moralis.RequestsPerSec,
		},
		Database: DatabaseConfig{
			Host:             tomlConfig.Database.Host,
			Port:             tomlConfig.Database.Port,
			User:             tomlConfig.Database.User,
			Password:         tomlConfig.Database.Password,
			DBName:           tomlConfig.Database.DBName,
			SSLMode:          tomlConfig.Database.SSLMode,
			MigrateOnStartup: tomlConfig.Database.MigrateOnStartup,
		},
		SocialData: SocialDataConfig{
			APIKey:                       tomlConfig.SocialData.APIKey,
			BaseURL:                      tomlConfig.SocialData.BaseURL,
			SearchKeywords:               tomlConfig.SocialData.SearchKeywords,
			PollingInterval:              time.Duration(tomlConfig.SocialData.PollingIntervalSec) * time.Second,
			RequestsPerSec:               tomlConfig.SocialData.RequestsPerSec,
			WebhookEnabled:               tomlConfig.SocialData.WebhookEnabled,
			WebhookEndpoint:              tomlConfig.SocialData.WebhookEndpoint,
			WebhookSecretKey:             tomlConfig.SocialData.WebhookSecretKey,
			InitialLookbackHours:         tomlConfig.SocialData.InitialLookbackHours,
			BlacklistedUsers:             tomlConfig.SocialData.BlacklistedUsers,
			TwitterLists:                 convertTomlTwitterLists(tomlConfig.SocialData.TwitterLists),
			PeriodicUpdateEnabled:        tomlConfig.SocialData.PeriodicUpdateEnabled,
			PeriodicUpdateIntervalHours:  tomlConfig.SocialData.PeriodicUpdateIntervalHours,
			PeriodicUpdateNewerThanHours: tomlConfig.SocialData.PeriodicUpdateNewerThanHours,
			PeriodicUpdateOlderThanHours: tomlConfig.SocialData.PeriodicUpdateOlderThanHours,
		},
		DexScreener: DexScreenerConfig{
			BaseURL:         tomlConfig.DexScreener.BaseURL,
			RequestsPerSec:  tomlConfig.DexScreener.RequestsPerSec,
			UpdateInterval:  time.Duration(tomlConfig.DexScreener.UpdateIntervalMin) * time.Minute,
			SupportedChains: supportedChains,
		},
		AI: AIConfig{
			Enabled:        tomlConfig.AI.Enabled,
			APIKey:         tomlConfig.AI.APIKey,
			Model:          tomlConfig.AI.Model,
			BaseURL:        tomlConfig.AI.BaseURL,
			MaxRetry:       tomlConfig.AI.MaxRetry,
			RequestsPerSec: tomlConfig.AI.RequestsPerSec,
		},
		Telegram: TelegramConfig{
			BotToken: tomlConfig.Telegram.BotToken,
			ChatID:   tomlConfig.Telegram.ChatID,
			Enabled:  tomlConfig.Telegram.Enabled,
		},
		Redis: RedisConfig{
			Host:                tomlConfig.Redis.Host,
			Port:                tomlConfig.Redis.Port,
			Password:            tomlConfig.Redis.Password,
			DB:                  tomlConfig.Redis.DB,
			PoolSize:            tomlConfig.Redis.PoolSize,
			MinIdleConns:        tomlConfig.Redis.MinIdleConns,
			MaxRetries:          tomlConfig.Redis.MaxRetries,
			DialTimeout:         time.Duration(tomlConfig.Redis.DialTimeoutSec) * time.Second,
			ReadTimeout:         time.Duration(tomlConfig.Redis.ReadTimeoutSec) * time.Second,
			WriteTimeout:        time.Duration(tomlConfig.Redis.WriteTimeoutSec) * time.Second,
			PoolTimeout:         time.Duration(tomlConfig.Redis.PoolTimeoutSec) * time.Second,
			IdleTimeout:         time.Duration(tomlConfig.Redis.IdleTimeoutSec) * time.Second,
			IdleCheckFrequency:  time.Duration(tomlConfig.Redis.IdleCheckFrequencySec) * time.Second,
			LockTimeout:         time.Duration(tomlConfig.Redis.LockTimeoutSec) * time.Second,
			LockRetryDelay:      time.Duration(tomlConfig.Redis.LockRetryDelaySec) * time.Second,
			LockMaxRetries:      tomlConfig.Redis.LockMaxRetries,
			LockRefreshInterval: time.Duration(tomlConfig.Redis.LockRefreshIntervalSec) * time.Second,
			LockCleanupTimeout:  time.Duration(tomlConfig.Redis.LockCleanupTimeoutSec) * time.Second,
			KeyPrefix:           tomlConfig.Redis.KeyPrefix,
			CleanupOldKeys:      tomlConfig.Redis.CleanupOldKeys,
			OldKeyPatterns:      tomlConfig.Redis.OldKeyPatterns,
		},
	}, nil
}

// Load loads configuration from a TOML file (backward compatibility)
func Load() (*Config, error) {
	return LoadWithFlags(nil)
}

// getConfigPath returns the path to the configuration file
func getConfigPath() string {
	// Check for CONFIG_FILE environment variable
	configPath := os.Getenv("CONFIG_FILE")
	if configPath != "" {
		return configPath
	}

	// Default config file paths to check, in order of preference
	configPaths := []string{
		"./service.toml",
		"./config/service.toml",
		"../../service.toml",
		"/etc/real-time-ca-service/service.toml",
	}

	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	// If no config file found, default to the first path
	return configPaths[0]
}
